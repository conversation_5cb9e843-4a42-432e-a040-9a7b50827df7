use std::{collections::HashSet, sync::Arc};

use axum::{
    Router,
    extract::{
        State, WebSocketUpgrade,
        ws::{Message, WebSocket},
    },
    response::IntoResponse,
    routing::get,
};
use futures_util::{sink::SinkExt, stream::StreamExt};
use tokio::sync::{Mutex, broadcast};
use tower_http::trace::{DefaultMakeSpan, TraceLayer};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

struct AppState {
    user_set: Mutex<HashSet<String>>,
    tx: broadcast::Sender<String>,
}

#[tokio::main]
async fn main() {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| {
                format!("{}=debug,tower_http=debug", env!("CARGO_CRATE_NAME")).into()
            }),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    let (tx, _rx) = broadcast::channel(100);
    let user_set = Mutex::new(HashSet::new());
    let app_state = Arc::new(AppState { user_set, tx });

    let app = Router::new()
        .route("/ws", get(handle_ws))
        .with_state(app_state)
        .layer(TraceLayer::new_for_http().make_span_with(DefaultMakeSpan::default()));

    let listener = tokio::net::TcpListener::bind("127.0.0.1:3000")
        .await
        .unwrap();
    tracing::info!("Started server successfully.");
    axum::serve(listener, app).await.unwrap();
}

async fn handle_ws(ws: WebSocketUpgrade, State(state): State<Arc<AppState>>) -> impl IntoResponse {
    ws.on_upgrade(|socket| handle_connection(socket, state))
}

async fn handle_connection(stream: WebSocket, state: Arc<AppState>) {
    // Can send and receive to only this client
    let (mut sender, mut receiver) = stream.split();
    let mut username = String::new();

    // Don't proceed until username is sent
    // Receive from one client and send only to that client
    while let Some(Ok(message)) = receiver.next().await {
        if let Message::Text(name) = message {
            if register_username(&name, &state).await {
                username = name.as_str().to_string();
                tracing::info!("Received username {username}");
                break;
            } else {
                match sender.send(Message::text("Username already taken.")).await {
                    Ok(_) => tracing::debug!("Sent username-taken message to client"),
                    Err(e) => tracing::error!("Failed to send username-taken message: {:?}", e),
                }
            }
        }
    }

    // tx and rx operate globally
    // They do not send messages to clients, but only communicate between channels
    // We need a copy of rx, it can only inherently provide info for one client
    // Otherwise only one client will see it, and nobody else will
    let mut rx = state.tx.subscribe();

    let msg = format!("{username} joined");
    tracing::debug!(msg);
    let _ = state.tx.send(msg);

    // Sends received message from another client (from recv_task) to this client
    let mut send_task = tokio::spawn(async move {
        while let Ok(msg) = rx.recv().await {
            if sender.send(Message::text(msg)).await.is_err() {
                break;
            }
        }
    });

    let tx = state.tx.clone();
    let name = username.clone();

    // Receives messages from this client and broadcasts to all other clients
    let mut recv_task = tokio::spawn(async move {
        while let Some(Ok(Message::Text(text))) = receiver.next().await {
            // Add username before message.
            let _ = tx.send(format!("{name}: {text}"));
        }
    });

    tokio::select! {
        _ = &mut send_task => recv_task.abort(),
        _ = &mut recv_task => send_task.abort(),
    };

    let msg = format!("{username} left.");
    tracing::debug!(msg);
    let _ = state.tx.send(msg);

    state.user_set.lock().await.remove(&username);
}

async fn register_username(name: &str, app_state: &AppState) -> bool {
    let mut user_set = app_state.user_set.lock().await;
    if !user_set.contains(name) {
        user_set.insert(name.to_owned());
        true
    } else {
        false
    }
}

