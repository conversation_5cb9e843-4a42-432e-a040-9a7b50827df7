    Blocking waiting for file lock on package cache
   0.649193083s  INFO prepare_target{force=false package_id=server v0.1.0 (/Users/<USER>/Projects/cola-test/server) target="server"}: cargo::core::compiler::fingerprint: fingerprint error for server v0.1.0 (/Users/<USER>/Projects/cola-test/server)/Check { test: false }/TargetInner { name: "server", doc: true, ..: with_path("/Users/<USER>/Projects/cola-test/server/src/main.rs", Edition2024) }
   0.649242917s  INFO prepare_target{force=false package_id=server v0.1.0 (/Users/<USER>/Projects/cola-test/server) target="server"}: cargo::core::compiler::fingerprint:     err: failed to read `/Users/<USER>/Projects/cola-test/server/target/debug/.fingerprint/server-168074035b1ed84f/bin-server`

Caused by:
    No such file or directory (os error 2)

Stack backtrace:
   0: std::backtrace::Backtrace::create
   1: cargo_util::paths::read_bytes
   2: cargo_util::paths::read
   3: cargo::core::compiler::fingerprint::_compare_old_fingerprint
   4: cargo::core::compiler::fingerprint::prepare_target
   5: cargo::core::compiler::compile
   6: <cargo::core::compiler::build_runner::BuildRunner>::compile
   7: cargo::ops::cargo_compile::compile_ws
   8: cargo::ops::cargo_compile::compile_with_exec
   9: cargo::ops::cargo_compile::compile
  10: cargo::commands::check::exec
  11: <cargo::cli::Exec>::exec
  12: cargo::main
  13: std::sys::backtrace::__rust_begin_short_backtrace::<fn(), ()>
  14: std::rt::lang_start::<()>::{closure#0}
  15: std::rt::lang_start_internal
  16: _main
   0.661229292s  INFO prepare_target{force=false package_id=server v0.1.0 (/Users/<USER>/Projects/cola-test/server) target="server"}: cargo::core::compiler::fingerprint: fingerprint error for server v0.1.0 (/Users/<USER>/Projects/cola-test/server)/Check { test: true }/TargetInner { name: "server", doc: true, ..: with_path("/Users/<USER>/Projects/cola-test/server/src/main.rs", Edition2024) }
   0.661252000s  INFO prepare_target{force=false package_id=server v0.1.0 (/Users/<USER>/Projects/cola-test/server) target="server"}: cargo::core::compiler::fingerprint:     err: failed to read `/Users/<USER>/Projects/cola-test/server/target/debug/.fingerprint/server-b118a86c50456497/test-bin-server`

Caused by:
    No such file or directory (os error 2)

Stack backtrace:
   0: std::backtrace::Backtrace::create
   1: cargo_util::paths::read_bytes
   2: cargo_util::paths::read
   3: cargo::core::compiler::fingerprint::_compare_old_fingerprint
   4: cargo::core::compiler::fingerprint::prepare_target
   5: cargo::core::compiler::compile
   6: <cargo::core::compiler::build_runner::BuildRunner>::compile
   7: cargo::ops::cargo_compile::compile_ws
   8: cargo::ops::cargo_compile::compile_with_exec
   9: cargo::ops::cargo_compile::compile
  10: cargo::commands::check::exec
  11: <cargo::cli::Exec>::exec
  12: cargo::main
  13: std::sys::backtrace::__rust_begin_short_backtrace::<fn(), ()>
  14: std::rt::lang_start::<()>::{closure#0}
  15: std::rt::lang_start_internal
  16: _main
    Checking server v0.1.0 (/Users/<USER>/Projects/cola-test/server)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 5.34s
