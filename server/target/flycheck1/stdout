{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.101","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/proc-macro2-1.0.101/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/proc-macro2-1.0.101/build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/proc-macro2-48dc86925ed1c73e/build-script-build"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.101","linked_libs":[],"linked_paths":[],"cfgs":["wrap_proc_macro","proc_macro_span_location","proc_macro_span_file"],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/proc-macro2-08260a3425b89f91/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#quote@1.0.41","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/quote-1.0.41/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/quote-1.0.41/build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/quote-d29f2e76da10b7c4/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#unicode-ident@1.0.20","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.20/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"unicode_ident","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicode-ident-1.0.20/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libunicode_ident-febc709a80fa938e.rlib","/Users/<USER>/Projects/cola-test/server/target/debug/deps/libunicode_ident-febc709a80fa938e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_core-1.0.228/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_core-1.0.228/build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","result","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/serde_core-cc322f98cbc6e012/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde@1.0.228","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde-1.0.228/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde-1.0.228/build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","derive","serde_derive","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/serde-0164d36e1229922b/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#libc@0.2.177","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/libc-0.2.177/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/libc-0.2.177/build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/libc-a4afc6c7e5b618ef/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#cfg-if@1.0.4","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/cfg-if-1.0.4/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"cfg_if","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/cfg-if-1.0.4/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libcfg_if-9377bebac931926b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#itoa@1.0.15","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/itoa-1.0.15/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"itoa","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/itoa-1.0.15/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libitoa-05cee0642efcc943.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#quote@1.0.41","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/quote-076adf1107724e7e/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.101","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/proc-macro2-1.0.101/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"proc_macro2","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/proc-macro2-1.0.101/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libproc_macro2-85a6839448a4ea82.rlib","/Users/<USER>/Projects/cola-test/server/target/debug/deps/libproc_macro2-85a6839448a4ea82.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/serde_core-9b69993af6987b63/out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#libc@0.2.177","linked_libs":[],"linked_paths":[],"cfgs":["freebsd12"],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/libc-e02b5f851fbce358/out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde@1.0.228","linked_libs":[],"linked_paths":[],"cfgs":["if_docsrs_then_no_serde_core"],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/serde-35ba4472f2ee923c/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-core@0.3.31","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-core-0.3.31/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_core","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-core-0.3.31/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libfutures_core-c4e2c119965b79ff.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#fnv@1.0.7","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/fnv-1.0.7/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"fnv","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/fnv-1.0.7/lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libfnv-e6d055e848b2c012.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pin-project-lite@0.2.16","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pin-project-lite-0.2.16/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pin_project_lite","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pin-project-lite-0.2.16/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libpin_project_lite-407bbb87017773e8.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#quote@1.0.41","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/quote-1.0.41/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"quote","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/quote-1.0.41/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","proc-macro"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libquote-2d340fae8d998104.rlib","/Users/<USER>/Projects/cola-test/server/target/debug/deps/libquote-2d340fae8d998104.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_core@1.0.228","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_core-1.0.228/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde_core","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_core-1.0.228/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","result","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserde_core-a46f5a26817476c5.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#libc@0.2.177","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/libc-0.2.177/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"libc","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/libc-0.2.177/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/liblibc-e5509510c7fd8f24.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#getrandom@0.3.4","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/getrandom-0.3.4/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/getrandom-0.3.4/build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/getrandom-a03154a3319d4e6f/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerocopy@0.8.27","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/zerocopy-0.8.27/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/zerocopy-0.8.27/build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["simd"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/zerocopy-ec93fdef9ebbe9ec/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#version_check@0.9.5","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/version_check-0.9.5/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"version_check","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/version_check-0.9.5/src/lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libversion_check-b26f5d9dc80fcfc1.rlib","/Users/<USER>/Projects/cola-test/server/target/debug/deps/libversion_check-b26f5d9dc80fcfc1.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#typenum@1.19.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/typenum-1.19.0/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/typenum-1.19.0/build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/typenum-c1647981f9074134/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#parking_lot_core@0.9.12","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/parking_lot_core-0.9.12/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/parking_lot_core-0.9.12/build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/parking_lot_core-4a2ba8e8d7c0092d/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#syn@2.0.107","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/syn-2.0.107/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"syn","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/syn-2.0.107/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["clone-impls","default","derive","extra-traits","full","parsing","printing","proc-macro","visit-mut"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsyn-5bee059ad3055011.rlib","/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsyn-5bee059ad3055011.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#typenum@1.19.0","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/typenum-06fb0dbd5dac965d/out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerocopy@0.8.27","linked_libs":[],"linked_paths":[],"cfgs":["zerocopy_core_error_1_81_0","zerocopy_diagnostic_on_unimplemented_1_78_0","zerocopy_generic_bounds_in_const_fn_1_61_0","zerocopy_target_has_atomics_1_60_0","zerocopy_aarch64_simd_1_59_0","zerocopy_panic_in_const_and_vec_try_reserve_1_57_0"],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/zerocopy-c51e6cb50be31aa4/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#generic-array@0.14.9","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/generic-array-0.14.9/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/generic-array-0.14.9/build.rs","edition":"2015","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["more_lengths"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/generic-array-fc22996727ec2e4f/build-script-build"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#getrandom@0.3.4","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/getrandom-ee37658aa4909fd5/out"}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#parking_lot_core@0.9.12","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/parking_lot_core-6f18d93f2301dd61/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#log@0.4.28","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/log-0.4.28/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"log","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/log-0.4.28/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/liblog-0f4495b6734ae15d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#scopeguard@1.2.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/scopeguard-1.2.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"scopeguard","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/scopeguard-1.2.0/src/lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libscopeguard-f061ba5e3d6e46f2.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_derive@1.0.228","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_derive-1.0.228/Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"serde_derive","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_derive-1.0.228/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserde_derive-7573c4f306828f6e.dylib"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#typenum@1.19.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/typenum-1.19.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"typenum","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/typenum-1.19.0/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtypenum-06ddc590903875b3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#zerocopy@0.8.27","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/zerocopy-0.8.27/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"zerocopy","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/zerocopy-0.8.27/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["simd"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libzerocopy-b398f89a459263fc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#getrandom@0.3.4","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/getrandom-0.3.4/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"getrandom","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/getrandom-0.3.4/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libgetrandom-1d387398ba0aa8f7.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#generic-array@0.14.9","linked_libs":[],"linked_paths":[],"cfgs":["relaxed_coherence","ga_is_deprecated"],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/generic-array-1751e798a5d38cff/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#lock_api@0.4.14","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/lock_api-0.4.14/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"lock_api","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/lock_api-0.4.14/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["atomic_usize","default"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/liblock_api-588a5b044527396b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio-macros@2.6.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-macros-2.6.0/Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"tokio_macros","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-macros-2.6.0/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtokio_macros-f1116dab6e3b8fad.dylib"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mio@1.1.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mio-1.1.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"mio","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mio-1.1.0/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["net","os-ext","os-poll"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libmio-600a6eb2352e889d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde@1.0.228","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde-1.0.228/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde-1.0.228/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","derive","serde_derive","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserde-13ead451a6491171.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ppv-lite86@0.2.21","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ppv-lite86-0.2.21/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ppv_lite86","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ppv-lite86-0.2.21/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["simd","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libppv_lite86-0a88b94020892874.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#generic-array@0.14.9","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/generic-array-0.14.9/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"generic_array","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/generic-array-0.14.9/src/lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["more_lengths"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libgeneric_array-436c10f1b846abe7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rand_core@0.9.3","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rand_core-0.9.3/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"rand_core","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rand_core-0.9.3/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["os_rng","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/librand_core-30c5dc65d35d1474.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#socket2@0.6.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/socket2-0.6.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"socket2","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/socket2-0.6.1/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["all"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsocket2-287c9f4ea4801db7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#signal-hook-registry@1.4.6","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/signal-hook-registry-1.4.6/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"signal_hook_registry","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/signal-hook-registry-1.4.6/src/lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsignal_hook_registry-2bb1295117025b5c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/httparse-1.10.1/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/httparse-1.10.1/build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/httparse-cf2d93bb4ba0378f/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#thiserror@2.0.17","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/thiserror-2.0.17/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/thiserror-2.0.17/build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/thiserror-19c3b98d0a3cb7ae/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#bytes@1.10.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bytes-1.10.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"bytes","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bytes-1.10.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","serde","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libbytes-047108cda3e279bc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#smallvec@1.15.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/smallvec-1.15.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"smallvec","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/smallvec-1.15.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["const_generics","const_new","serde","union"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsmallvec-8444140b7f4bb90c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rand_chacha@0.9.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rand_chacha-0.9.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"rand_chacha","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rand_chacha-0.9.0/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/librand_chacha-749cfa9c425800a4.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#thiserror@2.0.17","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/thiserror-ea13c3218148d05b/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#crypto-common@0.1.6","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/crypto-common-0.1.6/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"crypto_common","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/crypto-common-0.1.6/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libcrypto_common-db277d48df9865a2.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1","linked_libs":[],"linked_paths":[],"cfgs":["httparse_simd_neon_intrinsics","httparse_simd"],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/httparse-56b1f7395154f269/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#block-buffer@0.10.4","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/block-buffer-0.10.4/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"block_buffer","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/block-buffer-0.10.4/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libblock_buffer-572a33c8b6ddf77b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#thiserror-impl@2.0.17","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/thiserror-impl-2.0.17/Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"thiserror_impl","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/thiserror-impl-2.0.17/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libthiserror_impl-a57848880b592917.dylib"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http@1.3.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/http-1.3.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/http-1.3.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libhttp-95dcfa49de97a2c7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#parking_lot_core@0.9.12","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/parking_lot_core-0.9.12/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"parking_lot_core","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/parking_lot_core-0.9.12/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libparking_lot_core-e76fef0487c70bfa.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rand@0.9.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rand-0.9.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"rand","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rand-0.9.2/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","os_rng","small_rng","std","std_rng","thread_rng"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/librand-0513b28ec816a623.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#pin-utils@0.1.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pin-utils-0.1.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"pin_utils","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pin-utils-0.1.0/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libpin_utils-81f307a432a35de4.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#digest@0.10.7","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/digest-0.10.7/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"digest","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/digest-0.10.7/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","block-buffer","core-api","default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libdigest-9f5e5844decf6c77.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#thiserror@2.0.17","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/thiserror-2.0.17/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"thiserror","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/thiserror-2.0.17/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libthiserror-c6d08e087f55ac4d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/httparse-1.10.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"httparse","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/httparse-1.10.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libhttparse-695c18dc16694290.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#cpufeatures@0.2.17","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/cpufeatures-0.2.17/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"cpufeatures","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/cpufeatures-0.2.17/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libcpufeatures-f4b8e3766b81a69a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#parking_lot@0.12.5","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/parking_lot-0.12.5/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"parking_lot","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/parking_lot-0.12.5/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libparking_lot-f8af614f2b90dfb0.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-sink@0.3.31","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-sink-0.3.31/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_sink","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-sink-0.3.31/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libfutures_sink-1cb119b8339d70f9.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http-body@1.0.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/http-body-1.0.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http_body","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/http-body-1.0.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libhttp_body-cb84f0fd78127841.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#sha1@0.10.6","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sha1-0.10.6/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"sha1","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sha1-0.10.6/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsha1-e163a3f87826dada.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#slab@0.4.11","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/slab-0.4.11/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"slab","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/slab-0.4.11/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libslab-ac34334e9f8619ae.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#once_cell@1.21.3","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/once_cell-1.21.3/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"once_cell","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/once_cell-1.21.3/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","race","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libonce_cell-da4856566f8943d1.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-task@0.3.31","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-task-0.3.31/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_task","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-task-0.3.31/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libfutures_task-1ed11a2061084a10.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower-service@0.3.3","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tower-service-0.3.3/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower_service","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tower-service-0.3.3/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtower_service-913e12fbe522cb91.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio@1.48.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-1.48.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tokio","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-1.48.0/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["bytes","default","fs","full","io-std","io-util","libc","macros","mio","net","parking_lot","process","rt","rt-multi-thread","signal","signal-hook-registry","socket2","sync","time","tokio-macros"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtokio-de191ed92f393606.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-util@0.3.31","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-util-0.3.31/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_util","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-util-0.3.31/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","futures-sink","sink","slab","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libfutures_util-29f8b0309675d2bc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing-core@0.1.34","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-core-0.1.34/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tracing_core","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-core-0.1.34/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","once_cell","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtracing_core-de2e5df738e4b964.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#httpdate@1.0.3","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/httpdate-1.0.3/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"httpdate","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/httpdate-1.0.3/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libhttpdate-a4f6b3e82cf4fcf2.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#data-encoding@2.9.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/data-encoding-2.9.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"data_encoding","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/data-encoding-2.9.0/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libdata_encoding-2d5743e8a5b0ac5f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#utf-8@0.7.6","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/utf-8-0.7.6/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"utf8","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/utf-8-0.7.6/src/lib.rs","edition":"2015","doc":true,"doctest":true,"test":false},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libutf8-788e8de8c8d473a5.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#base64@0.22.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/base64-0.22.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"base64","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/base64-0.22.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libbase64-eed53d2e42885105.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing-attributes@0.1.30","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-attributes-0.1.30/Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"tracing_attributes","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-attributes-0.1.30/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtracing_attributes-8efbda9270c483cc.dylib"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#ryu@1.0.20","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ryu-1.0.20/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"ryu","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ryu-1.0.20/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libryu-e7bf65c8d72a1508.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower-layer@0.3.3","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tower-layer-0.3.3/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower_layer","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tower-layer-0.3.3/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtower_layer-e2d422ee90af68ed.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_json@1.0.145","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_json-1.0.145/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_json-1.0.145/build.rs","edition":"2021","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","raw_value","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/serde_json-f708eb3f758048b3/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing@0.1.41","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tracing","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["attributes","default","log","std","tracing-attributes"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtracing-79f1acc02e0340c7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http-body-util@0.1.3","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/http-body-util-0.1.3/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http_body_util","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/http-body-util-0.1.3/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libhttp_body_util-9143063b9f44edcc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#futures-channel@0.3.31","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-channel-0.3.31/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"futures_channel","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/futures-channel-0.3.31/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libfutures_channel-db23e9c556045e1c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#atomic-waker@1.1.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/atomic-waker-1.1.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"atomic_waker","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/atomic-waker-1.1.2/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libatomic_waker-28f7a4166c753479.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mime@0.3.17","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mime-0.3.17/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"mime","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mime-0.3.17/src/lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libmime-dc65d6ba3469e788.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_json@1.0.145","linked_libs":[],"linked_paths":[],"cfgs":["fast_arithmetic=\"64\""],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/serde_json-957acd6e02399b86/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#memchr@2.7.6","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/memchr-2.7.6/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"memchr","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/memchr-2.7.6/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libmemchr-899e154a36bff44f.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hyper@1.7.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/hyper-1.7.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hyper","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/hyper-1.7.0/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","http1","server"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libhyper-378fb76cf1e976fd.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#percent-encoding@2.3.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/percent-encoding-2.3.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"percent_encoding","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/percent-encoding-2.3.2/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libpercent_encoding-ed400f1484204cdd.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#engineioxide-core@0.2.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/engineioxide-core-0.2.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"engineioxide_core","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/engineioxide-core-0.2.0/src/lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libengineioxide_core-66f095a96b8c211d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#sync_wrapper@1.0.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sync_wrapper-1.0.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"sync_wrapper","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sync_wrapper-1.0.2/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsync_wrapper-65a26fa65e615517.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#unicase@2.8.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicase-2.8.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"unicase","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicase-2.8.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libunicase-0d7969adb653107b.rlib","/Users/<USER>/Projects/cola-test/server/target/debug/deps/libunicase-0d7969adb653107b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tungstenite@0.26.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tungstenite-0.26.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tungstenite","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tungstenite-0.26.2/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["data-encoding","handshake","http","httparse","sha1"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtungstenite-dfbb158b468ec95d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_json@1.0.145","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_json-1.0.145/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde_json","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_json-1.0.145/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","raw_value","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserde_json-7ba89953b5872729.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#hyper-util@0.1.17","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/hyper-util-0.1.17/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"hyper_util","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/hyper-util-0.1.17/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","http1","server","service","tokio"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libhyper_util-da62006210c6343e.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mime_guess@2.0.5","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mime_guess-2.0.5/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mime_guess-2.0.5/build.rs","edition":"2015","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/mime_guess-9206d1b60bd05138/build-script-build"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#form_urlencoded@1.2.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/form_urlencoded-1.2.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"form_urlencoded","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/form_urlencoded-1.2.2/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":false},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libform_urlencoded-6dd387cbfcf12b7d.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tungstenite@0.28.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tungstenite-0.28.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tungstenite","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tungstenite-0.28.0/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["data-encoding","handshake","http","httparse","sha1"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtungstenite-721eebdbf2a2ddf2.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#regex-syntax@0.8.8","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/regex-syntax-0.8.8/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"regex_syntax","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/regex-syntax-0.8.8/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libregex_syntax-dd80b02f14303140.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#matchit@0.8.4","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/matchit-0.8.4/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"matchit","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/matchit-0.8.4/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libmatchit-e22d24a635cd2fcc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rustversion@1.0.22","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rustversion-1.0.22/Cargo.toml","target":{"kind":["custom-build"],"crate_types":["bin"],"name":"build-script-build","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rustversion-1.0.22/build/build.rs","edition":"2018","doc":false,"doctest":false,"test":false},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/build/rustversion-31f05c62855dea0d/build-script-build"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#mime_guess@2.0.5","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[["MIME_TYPES_GENERATED_PATH","/Users/<USER>/Projects/cola-test/server/target/debug/build/mime_guess-e91aca1f85c6fadd/out/mime_types_generated.rs"]],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/mime_guess-e91aca1f85c6fadd/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#regex-automata@0.4.13","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/regex-automata-0.4.13/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"regex_automata","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/regex-automata-0.4.13/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","dfa-build","dfa-search","nfa-thompson","std","syntax"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libregex_automata-cc97fe24c03823fb.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_urlencoded@0.7.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_urlencoded-0.7.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde_urlencoded","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_urlencoded-0.7.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":false},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserde_urlencoded-ad86b99cdcc17189.rmeta"],"executable":null,"fresh":true}
{"reason":"build-script-executed","package_id":"registry+https://github.com/rust-lang/crates.io-index#rustversion@1.0.22","linked_libs":[],"linked_paths":[],"cfgs":[],"env":[],"out_dir":"/Users/<USER>/Projects/cola-test/server/target/debug/build/rustversion-bdf51f5c9187b371/out"}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio-tungstenite@0.28.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-tungstenite-0.28.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tokio_tungstenite","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-tungstenite-0.28.0/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["connect","default","handshake","stream"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtokio_tungstenite-5339ffe9bb5f3119.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#axum-core@0.5.5","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/axum-core-0.5.5/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"axum_core","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/axum-core-0.5.5/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["tracing"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libaxum_core-78d261dc7cfefa22.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#socketioxide-core@0.17.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/socketioxide-core-0.17.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"socketioxide_core","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/socketioxide-core-0.17.0/src/lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsocketioxide_core-0499a5cdcd044089.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower@0.5.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tower-0.5.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tower-0.5.2/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["__common","futures-core","futures-util","log","make","pin-project-lite","sync_wrapper","tokio","tracing","util"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtower-8344e82278806d0a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio-tungstenite@0.26.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-tungstenite-0.26.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tokio_tungstenite","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-tungstenite-0.26.2/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["connect","default","handshake","stream"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtokio_tungstenite-7a8ea6f482d38d4c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#headers-core@0.3.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/headers-core-0.3.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"headers_core","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/headers-core-0.3.0/src/lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libheaders_core-4cb6721b111be3c9.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#serde_path_to_error@0.1.20","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_path_to_error-0.1.20/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"serde_path_to_error","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_path_to_error-0.1.20/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserde_path_to_error-cb71a0858bb31cd7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#unicase@2.8.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicase-2.8.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"unicase","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/unicase-2.8.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libunicase-61fdab06621ffb2b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#lazy_static@1.5.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/lazy_static-1.5.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"lazy_static","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/lazy_static-1.5.0/src/lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/liblazy_static-d917dffe118813fb.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#rustversion@1.0.22","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rustversion-1.0.22/Cargo.toml","target":{"kind":["proc-macro"],"crate_types":["proc-macro"],"name":"rustversion","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rustversion-1.0.22/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":0,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/librustversion-7387b992f8417369.dylib"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#socketioxide-parser-common@0.17.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/socketioxide-parser-common-0.17.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"socketioxide_parser_common","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/socketioxide-parser-common-0.17.0/src/lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsocketioxide_parser_common-7daa35768470aff6.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#matchers@0.2.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/matchers-0.2.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"matchers","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/matchers-0.2.0/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libmatchers-253198e6f5add15a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#sharded-slab@0.1.7","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sharded-slab-0.1.7/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"sharded_slab","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sharded-slab-0.1.7/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsharded_slab-21d7146a230e260a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#axum@0.8.6","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/axum-0.8.6/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"axum","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/axum-0.8.6/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","form","http1","json","matched-path","original-uri","query","tokio","tower-log","tracing","ws"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libaxum-4f5428a766021acc.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#headers@0.4.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/headers-0.4.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"headers","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/headers-0.4.1/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libheaders-3d7a3a79aa90915c.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#mime_guess@2.0.5","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mime_guess-2.0.5/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"mime_guess","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mime_guess-2.0.5/src/lib.rs","edition":"2015","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libmime_guess-2d5204e2f09ee358.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#engineioxide@0.17.1","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/engineioxide-0.17.1/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"engineioxide","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/engineioxide-0.17.1/src/lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libengineioxide-a3b21ea0996b133a.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing-log@0.2.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-log-0.2.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tracing_log","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-log-0.2.0/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["log-tracer","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtracing_log-059307d53a1b4874.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tokio-util@0.7.16","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-util-0.7.16/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tokio_util","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tokio-util-0.7.16/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["io"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtokio_util-674852f07b309dc0.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#thread_local@1.1.9","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/thread_local-1.1.9/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"thread_local","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/thread_local-1.1.9/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libthread_local-c042c4e19089bb85.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#bitflags@2.10.0","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bitflags-2.10.0/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"bitflags","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/bitflags-2.10.0/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libbitflags-884ea8fb102192f3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#nu-ansi-term@0.50.3","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/nu-ansi-term-0.50.3/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"nu_ansi_term","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/nu-ansi-term-0.50.3/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","std"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libnu_ansi_term-e68e9b1ebdba37b4.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#http-range-header@0.4.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/http-range-header-0.4.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"http_range_header","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/http-range-header-0.4.2/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libhttp_range_header-2ca1611fb75df8f7.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#socketioxide@0.17.2","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/socketioxide-0.17.2/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"socketioxide","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/socketioxide-0.17.2/src/lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libsocketioxide-f381d882ea21b464.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#axum-extra@0.10.3","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/axum-extra-0.10.3/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"axum_extra","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/axum-extra-0.10.3/src/lib.rs","edition":"2021","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","tracing","typed-header"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libaxum_extra-e1efa5820f2560f6.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tower-http@0.6.6","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tower-http-0.6.6/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tower_http","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tower-http-0.6.6/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["default","fs","futures-core","futures-util","httpdate","mime","mime_guess","percent-encoding","set-status","tokio","tokio-util","trace","tracing"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtower_http-5cd89522bb8386c3.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"registry+https://github.com/rust-lang/crates.io-index#tracing-subscriber@0.3.20","manifest_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.20/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"tracing_subscriber","src_path":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.20/src/lib.rs","edition":"2018","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":["alloc","ansi","default","env-filter","fmt","matchers","nu-ansi-term","once_cell","registry","sharded-slab","smallvec","std","thread_local","tracing","tracing-log"],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libtracing_subscriber-182b4bae580e27ca.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"path+file:///Users/<USER>/Projects/cola-test/server#0.1.0","manifest_path":"/Users/<USER>/Projects/cola-test/server/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"server","src_path":"/Users/<USER>/Projects/cola-test/server/src/lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserver-b6a48356c21feb0b.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"path+file:///Users/<USER>/Projects/cola-test/server#0.1.0","manifest_path":"/Users/<USER>/Projects/cola-test/server/Cargo.toml","target":{"kind":["lib"],"crate_types":["lib"],"name":"server","src_path":"/Users/<USER>/Projects/cola-test/server/src/lib.rs","edition":"2024","doc":true,"doctest":true,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":true},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserver-21ce58e921e01653.rmeta"],"executable":null,"fresh":true}
{"reason":"compiler-artifact","package_id":"path+file:///Users/<USER>/Projects/cola-test/server#0.1.0","manifest_path":"/Users/<USER>/Projects/cola-test/server/Cargo.toml","target":{"kind":["bin"],"crate_types":["bin"],"name":"server","src_path":"/Users/<USER>/Projects/cola-test/server/src/main.rs","edition":"2024","doc":true,"doctest":false,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":true},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserver-b118a86c50456497.rmeta"],"executable":null,"fresh":false}
{"reason":"compiler-artifact","package_id":"path+file:///Users/<USER>/Projects/cola-test/server#0.1.0","manifest_path":"/Users/<USER>/Projects/cola-test/server/Cargo.toml","target":{"kind":["bin"],"crate_types":["bin"],"name":"server","src_path":"/Users/<USER>/Projects/cola-test/server/src/main.rs","edition":"2024","doc":true,"doctest":false,"test":true},"profile":{"opt_level":"0","debuginfo":2,"debug_assertions":true,"overflow_checks":true,"test":false},"features":[],"filenames":["/Users/<USER>/Projects/cola-test/server/target/debug/deps/libserver-168074035b1ed84f.rmeta"],"executable":null,"fresh":false}
{"reason":"build-finished","success":true}
