{"rustc": 9783247635632824192, "features": "[\"alloc\", \"futures-sink\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 336243669335521001, "path": 2092236943912772217, "deps": [[1615478164327904835, "pin_utils", false, 13196417188545913063], [1906322745568073236, "pin_project_lite", false, 1332372642368646298], [7013762810557009322, "futures_sink", false, 14063695590762264158], [7620660491849607393, "futures_core", false, 18299586989903635960], [14767213526276824509, "slab", false, 12662832754476332356], [16240732885093539806, "futures_task", false, 12943399375383862642]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-29f8b0309675d2bc/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}