{"rustc": 9783247635632824192, "features": "[\"default\", \"tracing\", \"typed-header\"]", "declared_features": "[\"__private_docs\", \"async-read-body\", \"async-stream\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"error-response\", \"file-stream\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"scheme\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 9646666107513749635, "path": 1238266197613485147, "deps": [[784494742817713399, "tower_service", false, 4547648140696491932], [1906322745568073236, "pin_project_lite", false, 1332372642368646298], [7435852374066785895, "headers", false, 7598714800073427545], [7712452662827335977, "tower_layer", false, 6068603017637044831], [8521753017138708247, "axum_core", false, 16146843721226398530], [8606274917505247608, "tracing", false, 17689875383156726446], [9010263965687315507, "http", false, 8437895014122088432], [10229185211513642314, "mime", false, 3371189259294213368], [10629569228670356391, "futures_util", false, 4517141958264938534], [11899261697793765154, "serde_core", false, 9668407999905511472], [14084095096285906100, "http_body", false, 7043866819259159195], [14156967978702956262, "rustversion", false, 14122303193053712315], [16066129441945555748, "bytes", false, 18184641800244184543], [16394803885710041359, "axum", false, 11055668037869165596], [16900715236047033623, "http_body_util", false, 6952192044500005287]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-extra-e1efa5820f2560f6/dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}