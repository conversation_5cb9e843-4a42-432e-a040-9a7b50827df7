{"rustc": 9783247635632824192, "features": "[\"alloc\", \"futures-sink\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17669703692130904899, "path": 2092236943912772217, "deps": [[1615478164327904835, "pin_utils", false, 7683250034233258975], [1906322745568073236, "pin_project_lite", false, 12767287659956176750], [7013762810557009322, "futures_sink", false, 3901318735189672287], [7620660491849607393, "futures_core", false, 5006159004132508997], [14767213526276824509, "slab", false, 11373363584540355110], [16240732885093539806, "futures_task", false, 4221652745982803987]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-124f19b972afd854/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}