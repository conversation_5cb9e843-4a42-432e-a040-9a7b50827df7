{"rustc": 9783247635632824192, "features": "[\"default\", \"form\", \"http1\", \"json\", \"matched-path\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\"]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 9646666107513749635, "path": 12497259255752494771, "deps": [[198136567835728122, "memchr", false, 12252053641726461507], [784494742817713399, "tower_service", false, 4547648140696491932], [1074175012458081222, "form_urlencoded", false, 15235874367703215595], [1906322745568073236, "pin_project_lite", false, 1332372642368646298], [2517136641825875337, "sync_wrapper", false, 10780331698741040758], [4162090052843532454, "hyper", false, 9946486011208148439], [5695049318159433696, "tower", false, 15730552383555536649], [5755145404821648512, "hyper_util", false, 15014042479878208342], [6803352382179706244, "percent_encoding", false, 5791881140053979504], [7695812897323945497, "itoa", false, 1143883502960461582], [7712452662827335977, "tower_layer", false, 6068603017637044831], [7720834239451334583, "tokio", false, 807912280106570173], [8521753017138708247, "axum_core", false, 16146843721226398530], [8606274917505247608, "tracing", false, 17689875383156726446], [8913795983780778928, "matchit", false, 6270693289490936654], [9010263965687315507, "http", false, 8437895014122088432], [10229185211513642314, "mime", false, 3371189259294213368], [10629569228670356391, "futures_util", false, 13624747197103166262], [11899261697793765154, "serde_core", false, 9668407999905511472], [12832915883349295919, "serde_json", false, 4281536530305801883], [14084095096285906100, "http_body", false, 7043866819259159195], [14814583949208169760, "serde_path_to_error", false, 17079097045072348747], [16066129441945555748, "bytes", false, 18184641800244184543], [16542808166767769916, "serde_urlencoded", false, 1007560971084986954], [16900715236047033623, "http_body_util", false, 6952192044500005287]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-fe0158e0d6512a7e/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}