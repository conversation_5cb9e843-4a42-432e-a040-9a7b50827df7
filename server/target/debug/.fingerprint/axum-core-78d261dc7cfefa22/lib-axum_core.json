{"rustc": 9783247635632824192, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 9646666107513749635, "path": 10868626855219318182, "deps": [[784494742817713399, "tower_service", false, 4547648140696491932], [1906322745568073236, "pin_project_lite", false, 1332372642368646298], [2517136641825875337, "sync_wrapper", false, 10780331698741040758], [7620660491849607393, "futures_core", false, 18299586989903635960], [7712452662827335977, "tower_layer", false, 6068603017637044831], [8606274917505247608, "tracing", false, 17689875383156726446], [9010263965687315507, "http", false, 8437895014122088432], [10229185211513642314, "mime", false, 3371189259294213368], [14084095096285906100, "http_body", false, 7043866819259159195], [16066129441945555748, "bytes", false, 18184641800244184543], [16900715236047033623, "http_body_util", false, 6952192044500005287]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-78d261dc7cfefa22/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}