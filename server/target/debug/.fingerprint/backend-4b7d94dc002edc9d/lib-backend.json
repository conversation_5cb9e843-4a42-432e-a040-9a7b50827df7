{"rustc": 9783247635632824192, "features": "[]", "declared_features": "[]", "target": 9849244431237625995, "profile": 6675295047989516842, "path": 10763286916239946207, "deps": [[702357104615633876, "axum_extra", false, 4663054138169133603], [1754976161352071619, "tracing_subscriber", false, 4202931747092101670], [2054153378684941554, "tower_http", false, 1633081987795862644], [5695049318159433696, "tower", false, 7475513067515034203], [7435852374066785895, "headers", false, 18022162103658659573], [7720834239451334583, "tokio", false, 5737921278941247429], [8606274917505247608, "tracing", false, 14696645453531857811], [10629569228670356391, "futures_util", false, 7680149958942285911], [11649034845009460065, "tokio_tungstenite", false, 15159063343753936499], [12832915883349295919, "serde_json", false, 12074804983226066010], [13548984313718623784, "serde", false, 7694278594605558852], [15279395123281218523, "socketioxide", false, 2663525535215038863], [16394803885710041359, "axum", false, 16289918507219481261]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/backend-4b7d94dc002edc9d/dep-lib-backend", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}