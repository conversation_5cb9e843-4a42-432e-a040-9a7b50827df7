{"rustc": 9783247635632824192, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 8252678305937259836, "path": 10868626855219318182, "deps": [[784494742817713399, "tower_service", false, 2863337789697448469], [1906322745568073236, "pin_project_lite", false, 12767287659956176750], [2517136641825875337, "sync_wrapper", false, 3617086105474052580], [7620660491849607393, "futures_core", false, 5006159004132508997], [7712452662827335977, "tower_layer", false, 11244959070701563502], [8606274917505247608, "tracing", false, 14696645453531857811], [9010263965687315507, "http", false, 15652444129710554109], [10229185211513642314, "mime", false, 13018609882737959030], [14084095096285906100, "http_body", false, 2852884589553329677], [16066129441945555748, "bytes", false, 16901858148974779171], [16900715236047033623, "http_body_util", false, 795489132293078133]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-a0093f9bd78c5789/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}