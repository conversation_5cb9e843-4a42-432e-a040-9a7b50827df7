{"rustc": 9783247635632824192, "features": "[\"default\", \"tracing\", \"typed-header\"]", "declared_features": "[\"__private_docs\", \"async-read-body\", \"async-stream\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"error-response\", \"file-stream\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"scheme\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 8252678305937259836, "path": 1238266197613485147, "deps": [[784494742817713399, "tower_service", false, 2863337789697448469], [1906322745568073236, "pin_project_lite", false, 12767287659956176750], [7435852374066785895, "headers", false, 18022162103658659573], [7712452662827335977, "tower_layer", false, 11244959070701563502], [8521753017138708247, "axum_core", false, 7685860580333008453], [8606274917505247608, "tracing", false, 14696645453531857811], [9010263965687315507, "http", false, 15652444129710554109], [10229185211513642314, "mime", false, 13018609882737959030], [10629569228670356391, "futures_util", false, 7680149958942285911], [11899261697793765154, "serde_core", false, 8711796956939620213], [14084095096285906100, "http_body", false, 2852884589553329677], [14156967978702956262, "rustversion", false, 14122303193053712315], [16066129441945555748, "bytes", false, 16901858148974779171], [16394803885710041359, "axum", false, 16289918507219481261], [16900715236047033623, "http_body_util", false, 795489132293078133]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-extra-4bf642ba40c6829e/dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}