{"rustc": 9783247635632824192, "features": "[\"default\", \"form\", \"http1\", \"json\", \"matched-path\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\"]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 8252678305937259836, "path": 12497259255752494771, "deps": [[198136567835728122, "memchr", false, 12735577940421639132], [784494742817713399, "tower_service", false, 2863337789697448469], [1074175012458081222, "form_urlencoded", false, 15263904755149982162], [1906322745568073236, "pin_project_lite", false, 12767287659956176750], [2517136641825875337, "sync_wrapper", false, 3617086105474052580], [4162090052843532454, "hyper", false, 3318449519200049856], [5695049318159433696, "tower", false, 17987748828542743882], [5755145404821648512, "hyper_util", false, 16083304131830428052], [6803352382179706244, "percent_encoding", false, 9553964075632482449], [7695812897323945497, "itoa", false, 14461488467652010852], [7712452662827335977, "tower_layer", false, 11244959070701563502], [7720834239451334583, "tokio", false, 3850581678001780715], [8521753017138708247, "axum_core", false, 7685860580333008453], [8606274917505247608, "tracing", false, 14696645453531857811], [8913795983780778928, "matchit", false, 12115548028097718560], [9010263965687315507, "http", false, 15652444129710554109], [10229185211513642314, "mime", false, 13018609882737959030], [10629569228670356391, "futures_util", false, 13645694466403281854], [11899261697793765154, "serde_core", false, 8711796956939620213], [12832915883349295919, "serde_json", false, 12074804983226066010], [14084095096285906100, "http_body", false, 2852884589553329677], [14814583949208169760, "serde_path_to_error", false, 8969439970369878047], [16066129441945555748, "bytes", false, 16901858148974779171], [16542808166767769916, "serde_urlencoded", false, 13186091486658936025], [16900715236047033623, "http_body_util", false, 795489132293078133]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-c3680c626a51cccc/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}