[package]
name = "cola-crdt"
version = "0.1.0"
edition = "2024"

[lib]
crate-type = ["cdylib", "rlib"] # cdylib for WASM, rlib for tests

[dependencies]
cola = { version = "0.5.1", features = ["serde"] }
serde = { version = "1.0.228", features = ["derive"] }
serde_json = "1.0"
wasm-bindgen = "0.2"
serde-wasm-bindgen = "0.6"
web-sys = { version = "0.3", features = ["console"] }

[target.'cfg(target_arch = "wasm32")'.dependencies]
getrandom = { version = "0.2", features = ["js"] }
console_error_panic_hook = "0.1"
