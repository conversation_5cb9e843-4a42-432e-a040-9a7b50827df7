use cola_crdt::text::DocText;

#[test]
fn test_two_peer_convergence() {
    let mut peer_1 = DocText::new("Hello, world", 1, true);
    let mut peer_2 = peer_1.fork(2);

    // Peer 1 deletes comma
    peer_1.delete(5..6);
    let msg_1 = peer_1.create_message();

    // Peer 2 adds exclamation
    peer_2.insert(12, "!");
    let msg_2 = peer_2.create_message();

    // Exchange operations
    peer_1.receive_message(msg_2);
    peer_2.receive_message(msg_1);

    // Both should converge to same state
    assert_eq!(peer_1.buffer, "Hello world!");
    assert_eq!(peer_2.buffer, "Hello world!");
}

#[test]
fn test_three_peer_convergence() {
    let mut peer_1 = DocText::new("abc", 1, true);
    let mut peer_2 = peer_1.fork(2);
    let mut peer_3 = peer_1.fork(3);

    // Each peer makes a change
    peer_1.insert(3, "1");
    let msg_1 = peer_1.create_message();

    peer_2.insert(0, "2");
    let msg_2 = peer_2.create_message();

    peer_3.insert(1, "3");
    let msg_3 = peer_3.create_message();

    // Peer 1 receives ops from 2 and 3
    peer_1.receive_message(msg_2.clone());
    peer_1.receive_message(msg_3.clone());

    // Peer 2 receives ops from 1 and 3
    peer_2.receive_message(msg_1.clone());
    peer_2.receive_message(msg_3.clone());

    // Peer 3 receives ops from 1 and 2
    peer_3.receive_message(msg_1);
    peer_3.receive_message(msg_2);

    // All peers should converge
    assert_eq!(peer_1.buffer, peer_2.buffer);
    assert_eq!(peer_2.buffer, peer_3.buffer);
}

#[test]
fn test_cursor_synchronization() {
    let mut peer_1 = DocText::new("Hello", 1, true);
    let mut peer_2 = peer_1.fork(2);

    // Peer 1 sets cursor at position 3
    peer_1.set_cursor(3);
    let msg = peer_1.create_message();

    // Peer 2 receives cursor update
    peer_2.receive_message(msg);

    // Both peers should see the cursor
    assert_eq!(peer_1.get_cursor_position(1), Some(3));
    assert_eq!(peer_2.get_cursor_position(1), Some(3));
}

#[test]
fn test_cursor_position_after_remote_edits() {
    let mut peer_1 = DocText::new("Hello", 1, true);
    let mut peer_2 = peer_1.fork(2);

    // Peer 1 sets cursor at end
    peer_1.set_cursor(5);
    let cursor_msg = peer_1.create_message();

    // Peer 2 receives cursor
    peer_2.receive_message(cursor_msg);

    // Peer 2 inserts at beginning
    peer_2.insert(0, "Hi ");
    let insert_msg = peer_2.create_message();

    // Peer 1 receives insert
    peer_1.receive_message(insert_msg);

    // Peer 1's cursor should have shifted
    assert_eq!(peer_1.get_cursor_position(1), Some(8));

    // Peer 2 should also see the shifted cursor
    assert_eq!(peer_2.get_cursor_position(1), Some(8));
}

#[test]
fn test_concurrent_inserts() {
    let mut peer_1 = DocText::new("", 1, true);
    let mut peer_2 = peer_1.fork(2);

    // Both insert at position 0 concurrently
    peer_1.insert(0, "A");
    let msg_1 = peer_1.create_message();

    peer_2.insert(0, "B");
    let msg_2 = peer_2.create_message();

    // Exchange operations
    peer_1.receive_message(msg_2);
    peer_2.receive_message(msg_1);

    // Should converge (order determined by CRDT, not timing)
    assert_eq!(peer_1.buffer, peer_2.buffer);
    assert!(peer_1.buffer == "AB" || peer_1.buffer == "BA");
}

#[test]
fn test_concurrent_delete_and_insert() {
    let mut peer_1 = DocText::new("Hello", 1, true);
    let mut peer_2 = peer_1.fork(2);

    // Peer 1 deletes "ll"
    peer_1.delete(2..4);
    let msg_1 = peer_1.create_message();

    // Peer 2 inserts at end
    peer_2.insert(5, " world");
    let msg_2 = peer_2.create_message();

    // Exchange operations
    peer_1.receive_message(msg_2);
    peer_2.receive_message(msg_1);

    // Should converge
    assert_eq!(peer_1.buffer, peer_2.buffer);
    assert_eq!(peer_1.buffer, "Heo world");
}

#[test]
fn test_multiple_operations_in_packet() {
    let mut peer_1 = DocText::new("abc", 1, true);
    let mut peer_2 = peer_1.fork(2);

    // Peer 1 does multiple operations
    peer_1.insert(3, "d");
    peer_1.insert(4, "e");
    peer_1.set_cursor(5);

    let msg = peer_1.create_message();

    // Send all at once
    peer_2.receive_message(msg);

    // Check results
    assert_eq!(peer_2.buffer, "abcde");
    assert_eq!(peer_2.get_cursor_position(1), Some(5));
}

#[test]
fn test_cursor_delete_synchronization() {
    let mut peer_1 = DocText::new("Hello", 1, true);
    let mut peer_2 = peer_1.fork(2);

    // Peer 1 sets cursor
    peer_1.set_cursor(3);
    let set_msg = peer_1.create_message();
    peer_2.receive_message(set_msg);

    // Verify cursor exists
    assert!(peer_2.get_cursor(1).is_some());

    // Peer 1 deletes cursor
    peer_1.delete_cursor();
    let delete_msg = peer_1.create_message();
    peer_2.receive_message(delete_msg);

    // Verify cursor removed
    assert!(peer_2.get_cursor(1).is_none());
}

#[test]
fn test_optimized_packet() {
    let mut peer_1 = DocText::new("Hello", 1, true);
    let mut peer_2 = peer_1.fork(2);

    // Create multiple cursor updates
    peer_1.insert(5, " world");
    peer_1.set_cursor(0);
    peer_1.set_cursor(5);
    peer_1.set_cursor(11);

    // Create and optimize packet
    let mut msg = peer_1.create_message();
    msg.optimize_cursor_updates();

    // Should have 1 text op + 1 cursor op
    assert_eq!(msg.updates.len(), 2);

    // Send optimized packet
    peer_2.receive_message(msg);

    // Check final state
    assert_eq!(peer_2.buffer, "Hello world");
    assert_eq!(peer_2.get_cursor_position(1), Some(11));
}

#[test]
fn test_out_of_order_delivery() {
    let mut peer_1 = DocText::new("abc", 1, true);
    let mut peer_2 = peer_1.fork(2);
    let mut peer_3 = peer_1.fork(3);

    // Peer 1 and 2 make operations
    peer_1.insert(3, "1");
    let msg_1 = peer_1.create_message();

    peer_2.insert(3, "2");
    let msg_2 = peer_2.create_message();

    // Peer 3 receives in different order than they were created (2 then 1)
    peer_3.receive_message(msg_2.clone());
    peer_3.receive_message(msg_1.clone());

    // Peer 1 receives in original order (just msg_2)
    peer_1.receive_message(msg_2);

    // Peer 2 receives msg_1
    peer_2.receive_message(msg_1);

    // All should converge despite different delivery order
    assert_eq!(peer_1.buffer, peer_2.buffer);
    assert_eq!(peer_2.buffer, peer_3.buffer);
}

#[test]
fn test_many_concurrent_edits() {
    let mut peer_1 = DocText::new("", 1, true);
    let mut peer_2 = peer_1.fork(2);
    let mut peer_3 = peer_1.fork(3);

    // Each peer makes multiple edits
    for i in 0..5 {
        peer_1.insert(0, format!("A{}", i));
        peer_2.insert(0, format!("B{}", i));
        peer_3.insert(0, format!("C{}", i));
    }

    let msg_1 = peer_1.create_message();
    let msg_2 = peer_2.create_message();
    let msg_3 = peer_3.create_message();

    // Broadcast all operations to all peers
    peer_1.receive_message(msg_2.clone());
    peer_1.receive_message(msg_3.clone());

    peer_2.receive_message(msg_1.clone());
    peer_2.receive_message(msg_3.clone());

    peer_3.receive_message(msg_1);
    peer_3.receive_message(msg_2);

    // All peers should converge to same state
    assert_eq!(peer_1.buffer, peer_2.buffer);
    assert_eq!(peer_2.buffer, peer_3.buffer);
}

#[test]
fn test_multiple_cursors_tracking() {
    let mut peer_1 = DocText::new("Hello", 1, true);
    let mut peer_2 = peer_1.fork(2);
    let mut peer_3 = peer_1.fork(3);

    // Each sets their cursor
    peer_1.set_cursor(0);
    let msg_1 = peer_1.create_message();

    peer_2.set_cursor(2);
    let msg_2 = peer_2.create_message();

    peer_3.set_cursor(5);
    let msg_3 = peer_3.create_message();

    // Broadcast to everyone
    peer_1.receive_message(msg_2.clone());
    peer_1.receive_message(msg_3.clone());

    peer_2.receive_message(msg_1.clone());
    peer_2.receive_message(msg_3.clone());

    peer_3.receive_message(msg_1);
    peer_3.receive_message(msg_2);

    // Each peer should see all 3 cursors
    assert_eq!(peer_1.get_all_cursor_positions().len(), 3);
    assert_eq!(peer_2.get_all_cursor_positions().len(), 3);
    assert_eq!(peer_3.get_all_cursor_positions().len(), 3);

    // And they should agree on positions
    assert_eq!(
        peer_1.get_all_cursor_positions(),
        peer_2.get_all_cursor_positions()
    );
    assert_eq!(
        peer_2.get_all_cursor_positions(),
        peer_3.get_all_cursor_positions()
    );
}
