use cola::ReplicaId;
use serde::{Deserialize, Serialize};

use crate::text::Operation;

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct UpdateMessage {
    pub sender_id: ReplicaId,
    pub updates: Vec<Operation>,
}

impl UpdateMessage {
    /// Optimizes cursor updates by keeping only the most recent cursor position.
    /// Since cursor updates supersede previous ones, we only need the last one.
    /// Text operations are preserved in order.
    pub fn optimize_cursor_updates(&mut self) {
        let mut text_ops = Vec::new();
        let mut last_cursor_op: Option<Operation> = None;

        for operation in self.updates.drain(..) {
            match operation {
                Operation::CursorUpdate(_) | Operation::CursorDelete => {
                    // Keep only the most recent cursor operation
                    last_cursor_op = Some(operation);
                }
                op @ (Operation::TextInsert(_) | Operation::TextDelete(_)) => {
                    text_ops.push(op);
                }
            }
        }

        // Rebuild: text operations first, then final cursor state
        self.updates = text_ops;
        if let Some(cursor_op) = last_cursor_op {
            self.updates.push(cursor_op);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::text::DocText;

    #[test]
    fn test_update_message_creation() {
        let msg = UpdateMessage {
            sender_id: 1,
            updates: vec![],
        };

        assert_eq!(msg.sender_id, 1);
        assert!(msg.updates.is_empty());
    }

    #[test]
    fn test_optimize_no_cursor_updates() {
        let mut doc = DocText::new("Hello", 1, true);
        doc.insert(5, " world");
        doc.insert(11, "!");

        let mut msg = doc.create_message();

        msg.optimize_cursor_updates();
        assert_eq!(msg.updates.len(), 2); // Both text ops preserved
    }

    #[test]
    fn test_optimize_multiple_cursor_updates() {
        let mut doc = DocText::new("Hello", 1, true);
        doc.set_cursor(0);
        doc.set_cursor(3);
        doc.set_cursor(5);

        let mut msg = doc.create_message();

        msg.optimize_cursor_updates();
        assert_eq!(msg.updates.len(), 1); // Only last cursor kept
        assert!(matches!(msg.updates[0], Operation::CursorUpdate(_)));
    }

    #[test]
    fn test_optimize_mixed_operations() {
        let mut doc = DocText::new("Hello", 1, true);
        doc.insert(5, " world");
        doc.set_cursor(0);
        doc.delete(0..1);
        doc.set_cursor(3);

        let mut msg = doc.create_message();

        msg.optimize_cursor_updates();

        // Should have 2 text ops + 1 cursor op
        assert_eq!(msg.updates.len(), 3);

        // First two should be text ops
        assert!(matches!(msg.updates[0], Operation::TextInsert(_)));
        assert!(matches!(msg.updates[1], Operation::TextDelete(_)));

        // Last should be cursor
        assert!(matches!(msg.updates[2], Operation::CursorUpdate(_)));
    }

    #[test]
    fn test_optimize_cursor_delete() {
        let mut doc = DocText::new("Hello", 1, true);
        doc.set_cursor(0);
        doc.delete_cursor();

        let mut msg = doc.create_message();

        msg.optimize_cursor_updates();

        // Should only have the delete
        assert_eq!(msg.updates.len(), 1);
        assert!(matches!(msg.updates[0], Operation::CursorDelete));
    }
}
