use crate::message::UpdateMessage;
use crate::text::DocText;
use wasm_bindgen::prelude::*;

/// WASM wrapper for the Document struct
/// This provides a JavaScript-friendly API for the CRDT document
#[wasm_bindgen(js_name = DocText)]
pub struct WasmDocText {
    doc: DocText,
}

#[wasm_bindgen(js_class = DocText)]
impl WasmDocText {
    /// Create a new document with initial text and a unique replica ID
    #[wasm_bindgen(constructor)]
    pub fn new(initial_text: String, replica_id: u64, gc: bool) -> Self {
        #[cfg(target_arch = "wasm32")]
        console_error_panic_hook::set_once();
        Self {
            doc: DocText::new(initial_text, replica_id, gc),
        }
    }

    /// Forks an existing DocText
    pub fn fork(&self, replica_id: u64) -> Self {
        Self {
            doc: self.doc.fork(replica_id),
        }
    }

    /// Get the current text content of the document
    #[wasm_bindgen(getter)]
    pub fn text(&self) -> String {
        self.doc.buffer.clone()
    }

    /// Get this document's replica ID
    #[wasm_bindgen(getter, js_name = replicaId)]
    pub fn replica_id(&self) -> u64 {
        self.doc.crdt.id()
    }

    /// Insert text at a position
    /// Operations are stored internally, call createMessage() to get them
    pub fn insert(&mut self, position: usize, text: String) {
        self.doc.insert(position, text);
    }

    /// Delete a range of text
    /// Operations are stored internally, call createMessage() to get them
    pub fn delete(&mut self, start: usize, end: usize) {
        self.doc.delete(start..end);
    }

    /// Set the cursor position
    /// Operations are stored internally, call createMessage() to get them
    #[wasm_bindgen(js_name = setCursor)]
    pub fn set_cursor(&mut self, position: usize) {
        self.doc.set_cursor(position);
    }

    /// Delete the cursor
    /// Operations are stored internally, call createMessage() to get them
    #[wasm_bindgen(js_name = deleteCursor)]
    pub fn delete_cursor(&mut self) {
        self.doc.delete_cursor();
    }

    /// Receive and apply an update message from another peer
    /// Expects a serialized UpdateMessage
    #[wasm_bindgen(js_name = receiveMessage)]
    pub fn receive_message(&mut self, js_msg: JsValue) -> Result<(), JsValue> {
        let msg: UpdateMessage = serde_wasm_bindgen::from_value(js_msg)
            .map_err(|e| JsValue::from_str(&format!("Failed to deserialize message: {}", e)))?;

        self.doc.receive_message(msg);
        Ok(())
    }

    /// Create an UpdateMessage from all pending operations
    /// This drains the internal operations queue and returns a message to send
    #[wasm_bindgen(js_name = createMessage)]
    pub fn create_message(&mut self) -> JsValue {
        let msg = self.doc.create_message();
        serde_wasm_bindgen::to_value(&msg).unwrap()
    }

    /// Get cursor position for a specific replica
    #[wasm_bindgen(js_name = getCursorPosition)]
    pub fn get_cursor_position(&self, replica_id: u64) -> Option<usize> {
        self.doc.get_cursor_position(replica_id)
    }

    /// Get all cursor positions as a JSON object mapping replica_id -> position
    #[wasm_bindgen(js_name = getAllCursorPositions)]
    pub fn get_all_cursor_positions(&self) -> JsValue {
        let positions = self.doc.get_all_cursor_positions();
        serde_wasm_bindgen::to_value(&positions).unwrap()
    }
}

#[wasm_bindgen]
pub fn log(message: &str) {
    web_sys::console::log_1(&JsValue::from_str(message));
}
