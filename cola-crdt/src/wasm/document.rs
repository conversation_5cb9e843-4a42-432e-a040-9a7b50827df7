use super::text::WasmDocText;
use crate::document::Document;
use wasm_bindgen::prelude::*;

/// WASM wrapper for the Document struct
/// This provides a JavaScript-friendly API for managing multiple text documents
#[wasm_bindgen(js_name = Document)]
pub struct WasmDocument {
    doc: Document,
}

#[wasm_bindgen(js_class = Document)]
impl WasmDocument {
    /// Create a new document with a unique replica ID
    #[wasm_bindgen(constructor)]
    pub fn new(replica_id: u64) -> Self {
        #[cfg(target_arch = "wasm32")]
        console_error_panic_hook::set_once();
        Self {
            doc: Document::new(replica_id),
        }
    }

    /// Get this document's replica ID
    #[wasm_bindgen(getter, js_name = replicaId)]
    pub fn replica_id(&self) -> u64 {
        self.doc.replica_id
    }

    /// Get a copy of a text document with a new replica ID
    /// Returns null if the key doesn't exist
    #[wasm_bindgen(js_name = getCopy)]
    pub fn get_copy(&self, key: &str, new_replica_id: u64) -> Option<WasmDocText> {
        self.doc
            .get_copy(key, new_replica_id)
            .map(|doc_text| WasmDocText::from_doc_text(doc_text))
    }

    /// Add a text document to this document
    /// Returns the previous document if one existed with the same key
    #[wasm_bindgen(js_name = addText)]
    pub fn add_text(&mut self, key: &str, text: &WasmDocText) -> Option<WasmDocText> {
        let doc_text = text.clone_doc_text();
        self.doc
            .add_text(key, doc_text)
            .map(|doc_text| WasmDocText::from_doc_text(doc_text))
    }

    /// Delete a text document from this document
    /// Returns the deleted document if it existed
    #[wasm_bindgen(js_name = deleteText)]
    pub fn delete_text(&mut self, key: &str) -> Option<WasmDocText> {
        self.doc
            .delete_text(key)
            .map(|doc_text| WasmDocText::from_doc_text(doc_text))
    }

    /// Check if a text document exists with the given key
    #[wasm_bindgen(js_name = hasText)]
    pub fn has_text(&self, key: &str) -> bool {
        self.doc.texts.contains_key(key)
    }

    /// Get all text document keys
    #[wasm_bindgen(js_name = getTextKeys)]
    pub fn get_text_keys(&self) -> Vec<String> {
        self.doc.texts.keys().cloned().collect()
    }

    /// Get or create a text document with the given key
    /// If the document doesn't exist, it will be created with the provided initial text
    #[wasm_bindgen(js_name = getOrCreate)]
    pub fn get_or_create(&mut self, key: &str, initial_text: &WasmDocText) -> WasmDocText {
        let doc_text = initial_text.clone_doc_text();
        let result = self.doc.get_or_create(key, doc_text);
        WasmDocText::from_doc_text(result.fork(result.crdt.id()))
    }
}

