use std::collections::HashMap;

use cola::ReplicaId;

use crate::text::{Curs<PERSON>, DocText};

pub struct Document {
    pub texts: HashMap<String, DocText>,
    pub replica_id: ReplicaId,
    pub cursor: Option<Cursor>,
}

impl Document {
    pub fn new(replica_id: ReplicaId) -> Self {
        Self {
            texts: HashMap::new(),
            replica_id,
            cursor: None,
        }
    }

    pub fn get_copy(&self, key: &str, new_replica_id: ReplicaId) -> Option<DocText> {
        Some(self.texts.get(key)?.fork(new_replica_id))
    }

    pub fn get_or_create(&mut self, key: &str, initial_text: DocText) -> &mut DocText {
        self.texts.entry(key.to_string()).or_insert(initial_text)
    }

    pub fn add_text(&mut self, key: &str, text: DocText) -> Option<DocText> {
        self.texts.insert(key.to_string(), text)
    }

    pub fn delete_text(&mut self, key: &str) -> Option<DocText> {
        self.texts.remove(key)
    }
}
